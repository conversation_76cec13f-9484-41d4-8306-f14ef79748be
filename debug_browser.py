#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器调试脚本
用于调试浏览器界面元素，帮助找到正确的地址栏和搜索按钮
"""

import time
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

class BrowserDebugger:
    def __init__(self):
        self.poco = None
        self.browser_package = BROWSER_CONFIG["package_name"]
        
    def connect_device(self):
        """连接设备"""
        try:
            # 手动连接第一个可用设备
            adb = ADB()
            devices = adb.devices()
            
            if not devices:
                print("❌ 未发现任何设备")
                return False
            
            # 连接第一个设备
            device_id = devices[0][0]
            connect_device(f"Android:///{device_id}")
            print(f"📱 已连接设备: {device_id}")
            
            # 初始化 Poco
            self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            print("✅ 设备连接成功")
            return True
        except Exception as e:
            print(f"❌ 设备连接失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        try:
            print("🚀 正在打开浏览器...")
            start_app(self.browser_package)
            time.sleep(3)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    
    def analyze_browser_elements(self):
        """分析浏览器界面元素"""
        print("\n🔍 开始分析浏览器界面元素...")
        print("=" * 60)
        
        try:
            # 获取所有元素
            all_elements = self.poco("*")
            print(f"📊 总共发现 {len(all_elements)} 个界面元素")
            
            # 分析可能的地址栏元素
            print("\n🎯 可能的地址栏元素:")
            print("-" * 30)
            
            address_bar_keywords = ["url", "address", "location", "omnibox", "search"]
            found_address_elements = []
            
            for element in all_elements:
                try:
                    resource_id = element.attr("resourceId") if element.attr("resourceId") else ""
                    text_content = element.get_text() if element.get_text() else ""
                    class_name = element.attr("className") if element.attr("className") else ""
                    
                    # 检查是否可能是地址栏
                    is_address_bar = False
                    for keyword in address_bar_keywords:
                        if (keyword.lower() in resource_id.lower() or 
                            keyword.lower() in text_content.lower() or
                            keyword.lower() in class_name.lower()):
                            is_address_bar = True
                            break
                    
                    if is_address_bar:
                        found_address_elements.append({
                            "resourceId": resource_id,
                            "text": text_content,
                            "className": class_name
                        })
                        print(f"  📍 ResourceId: {resource_id}")
                        print(f"     Text: {text_content}")
                        print(f"     ClassName: {class_name}")
                        print()
                        
                except:
                    continue
            
            # 分析可能的搜索按钮元素
            print("\n🔍 可能的搜索/前往按钮元素:")
            print("-" * 30)
            
            search_button_keywords = ["go", "search", "navigate", "前往", "搜索", "button"]
            found_button_elements = []
            
            for element in all_elements:
                try:
                    resource_id = element.attr("resourceId") if element.attr("resourceId") else ""
                    text_content = element.get_text() if element.get_text() else ""
                    class_name = element.attr("className") if element.attr("className") else ""
                    
                    # 检查是否可能是搜索按钮
                    is_search_button = False
                    for keyword in search_button_keywords:
                        if (keyword.lower() in resource_id.lower() or 
                            keyword.lower() in text_content.lower() or
                            keyword.lower() in class_name.lower()):
                            is_search_button = True
                            break
                    
                    if is_search_button:
                        found_button_elements.append({
                            "resourceId": resource_id,
                            "text": text_content,
                            "className": class_name
                        })
                        print(f"  🔘 ResourceId: {resource_id}")
                        print(f"     Text: {text_content}")
                        print(f"     ClassName: {class_name}")
                        print()
                        
                except:
                    continue
            
            # 输出所有文本元素（可能包含搜索相关的文本）
            print("\n📝 所有文本元素:")
            print("-" * 30)
            
            text_elements = []
            for element in all_elements:
                try:
                    text_content = element.get_text()
                    if text_content and text_content.strip():
                        text_elements.append(text_content.strip())
                except:
                    continue
            
            # 去重并排序
            unique_texts = list(set(text_elements))
            unique_texts.sort()
            
            for i, text in enumerate(unique_texts[:20]):  # 只显示前20个
                print(f"  {i+1:2d}. {text}")
            
            if len(unique_texts) > 20:
                print(f"     ... 还有 {len(unique_texts) - 20} 个文本元素")
            
            print(f"\n📊 分析结果:")
            print(f"   - 可能的地址栏元素: {len(found_address_elements)} 个")
            print(f"   - 可能的搜索按钮元素: {len(found_button_elements)} 个")
            print(f"   - 总文本元素: {len(unique_texts)} 个")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return False
    
    def test_address_bar_click(self):
        """测试地址栏点击"""
        print("\n🧪 测试地址栏点击...")
        print("-" * 30)
        
        # 尝试不同的地址栏选择器
        selectors = [
            "com.android.browser:id/url",
            "android:id/url",
            "url",
            "address_bar",
            "omnibox",
            "com.android.browser:id/location_bar",
            "com.android.browser:id/url_bar"
        ]
        
        for selector in selectors:
            try:
                if self.poco(resourceId=selector).exists():
                    print(f"✅ 找到地址栏: {selector}")
                    self.poco(resourceId=selector).click()
                    time.sleep(1)
                    print("   点击成功，等待1秒...")
                    return True
                else:
                    print(f"❌ 未找到: {selector}")
            except Exception as e:
                print(f"❌ 测试 {selector} 失败: {e}")
        
        # 尝试文本方式
        texts = ["搜索或输入网址", "输入网址", "地址栏", "Search or type URL"]
        for text in texts:
            try:
                if self.poco(text=text).exists():
                    print(f"✅ 通过文本找到地址栏: {text}")
                    self.poco(text=text).click()
                    time.sleep(1)
                    print("   点击成功，等待1秒...")
                    return True
                else:
                    print(f"❌ 未找到文本: {text}")
            except Exception as e:
                print(f"❌ 测试文本 {text} 失败: {e}")
        
        return False
    
    def run_debug(self):
        """运行调试"""
        print("🐛 浏览器调试工具")
        print("=" * 50)
        
        if not self.connect_device():
            return False
        
        if not self.open_browser():
            return False
        
        # 等待浏览器完全加载
        print("\n⏳ 等待浏览器加载...")
        time.sleep(3)
        
        # 分析界面元素
        self.analyze_browser_elements()
        
        # 测试地址栏点击
        self.test_address_bar_click()
        
        print("\n✅ 调试完成!")
        print("\n💡 建议:")
        print("   1. 查看上面的分析结果")
        print("   2. 找到正确的地址栏和搜索按钮元素")
        print("   3. 更新 xhs_automation.py 中的选择器")
        
        return True

def main():
    debugger = BrowserDebugger()
    debugger.run_debug()

if __name__ == "__main__":
    main()
