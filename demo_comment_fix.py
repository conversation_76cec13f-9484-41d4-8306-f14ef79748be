#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评论提取修复演示脚本
展示修复后的评论提取功能，确保只从"共有X条评论"下方提取真正的评论内容
"""

from xhs_automation import XHSAutomation

def main():
    """演示主函数"""
    print("🎯 小红书评论提取修复演示")
    print("=" * 50)
    print("本演示将展示修复后的评论提取功能：")
    print("✅ 智能识别'共有X条评论'标识")
    print("✅ 只从该标识下方提取真正的评论内容")
    print("✅ 过滤界面元素和无关文本")
    print("✅ 提供详细的提取过程信息")
    print("-" * 50)
    
    # 创建自动化实例
    automation = XHSAutomation()
    
    print("📱 正在连接设备...")
    if not automation.connect_device():
        print("❌ 设备连接失败，请检查:")
        print("   1. 设备是否通过 USB 连接")
        print("   2. 是否开启了开发者选项")
        print("   3. 是否开启了 USB 调试")
        return False
    
    print("\n🚀 正在打开浏览器...")
    if not automation.open_browser():
        print("❌ 浏览器打开失败，请检查:")
        print("   1. 设备是否安装了默认浏览器")
        print("   2. 浏览器包名是否正确")
        return False
    
    print("\n🔍 正在导航到小红书链接...")
    if not automation.navigate_to_url():
        print("❌ 导航失败，请检查:")
        print("   1. 网络连接是否正常")
        print("   2. 链接是否有效")
        return False
    
    print("\n🔐 正在处理权限弹窗...")
    automation.handle_permissions()
    
    print("\n⏳ 等待页面加载...")
    automation.wait_for_xhs_page()
    
    print("\n" + "="*60)
    print("🧪 开始演示评论提取功能")
    print("="*60)
    
    # 演示评论提取过程
    print("\n📍 步骤1: 查找评论区域标识")
    print("-" * 30)
    
    # 手动调用查找评论区域方法
    comment_section = automation._find_comment_section_start()
    
    if comment_section:
        print(f"✅ 成功找到评论区域标识: '{comment_section['text']}'")
        print(f"📍 位置坐标: ({comment_section.get('x_position', 'N/A')}, {comment_section['y_position']:.0f})")
    else:
        print("❌ 未找到评论区域标识")
        print("💡 这可能意味着:")
        print("   1. 页面还在加载中")
        print("   2. 页面结构与预期不同")
        print("   3. 需要滑动到评论区域")
    
    print("\n📍 步骤2: 提取评论内容")
    print("-" * 30)
    
    # 提取当前视图的评论
    current_comments = automation.extract_comments_from_current_view()
    
    if current_comments:
        print(f"✅ 成功提取 {len(current_comments)} 条评论")
        print("\n📝 提取到的评论内容:")
        for i, comment in enumerate(current_comments[:5], 1):  # 只显示前5条
            print(f"   {i}. {comment}")
        
        if len(current_comments) > 5:
            print(f"   ... 还有 {len(current_comments) - 5} 条评论")
    else:
        print("❌ 当前视图未提取到评论")
        print("💡 可能的原因:")
        print("   1. 需要滑动到评论区域")
        print("   2. 评论内容被过滤掉了")
        print("   3. 页面结构特殊")
    
    print("\n📍 步骤3: 滑动收集更多评论")
    print("-" * 30)
    
    # 询问用户是否继续滑动收集
    user_choice = input("是否继续滑动收集更多评论？(y/n): ").lower().strip()
    
    if user_choice == 'y':
        print("🔄 开始滑动收集评论...")
        
        # 滑动收集评论（减少滑动次数用于演示）
        if automation.scroll_and_collect_comments(max_scrolls=3):
            print("\n🎉 评论收集完成!")
            automation.print_comments()
        else:
            print("❌ 评论收集失败")
    else:
        print("⏭️ 跳过滑动收集")
    
    print("\n" + "="*60)
    print("📊 演示总结")
    print("="*60)
    
    print("🔧 修复内容:")
    print("   ✅ 智能识别评论区域标识（共有X条评论）")
    print("   ✅ 按位置排序，只提取标识下方的内容")
    print("   ✅ 增强的评论内容过滤")
    print("   ✅ 排除界面元素和时间戳")
    print("   ✅ 详细的调试信息输出")
    
    print("\n💡 使用建议:")
    if comment_section and current_comments:
        print("   🎉 评论提取功能工作正常!")
        print("   📝 现在可以运行完整的自动化脚本")
        print("   🔄 建议适当调整滑动次数以获取更多评论")
    elif comment_section:
        print("   ⚠️ 找到了评论区域但未提取到评论")
        print("   🔄 建议滑动到评论内容区域")
        print("   ⚙️ 可能需要调整过滤条件")
    else:
        print("   ❌ 未找到评论区域标识")
        print("   📱 请确认页面已完全加载")
        print("   🔄 可能需要手动滑动到评论区域")
    
    print("\n✅ 演示完成!")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
