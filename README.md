# 小红书自动化评论收集脚本

这是一个使用 Airtest 框架开发的自动化脚本，用于在手机上打开浏览器，导航到指定的小红书帖子，并自动收集评论内容。

## 功能特性

- 🤖 自动连接 Android 设备
- 🌐 自动打开浏览器并导航到指定链接
- 🔐 自动处理权限弹窗
- 📜 自动滑动页面收集评论
- 💬 智能过滤和提取评论内容
- 📊 在控制台输出收集到的评论

## 环境要求

### 系统要求
- Python 3.6+
- Android 设备（已开启开发者选项和USB调试）
- ADB 工具

### Python 依赖
```bash
pip install airtest pocoui
```

## 使用方法

### 1. 准备工作

1. **连接 Android 设备**
   - 确保设备已开启开发者选项
   - 开启 USB 调试模式
   - 通过 USB 连接到电脑

2. **验证 ADB 连接**
   ```bash
   adb devices
   ```
   确保能看到你的设备

3. **确保浏览器已安装**
   - 默认使用 `com.android.browser`
   - 如需使用其他浏览器，请修改 `config.py` 中的包名

### 2. 运行脚本

```bash
python3 xhs_automation.py
```

### 3. 脚本执行流程

1. **设备连接** - 自动连接第一个可用的 Android 设备
2. **打开浏览器** - 启动指定的浏览器应用
3. **导航到链接** - 在地址栏输入小红书帖子链接
4. **处理权限** - 自动点击"允许"等权限按钮
5. **等待加载** - 等待小红书页面完全加载
6. **收集评论** - 滑动页面并提取评论内容
7. **输出结果** - 在控制台显示收集到的评论

## 配置说明

可以通过修改 `config.py` 文件来调整脚本行为：

- `DEVICE_CONFIG`: 设备连接配置
- `BROWSER_CONFIG`: 浏览器配置
- `URL_CONFIG`: 目标链接配置
- `SCROLL_CONFIG`: 滑动行为配置
- `COMMENT_FILTER`: 评论过滤配置
- `PERMISSION_CONFIG`: 权限处理配置

## 注意事项

1. **设备兼容性**
   - 脚本主要针对 Android 设备开发
   - 不同品牌的设备可能需要调整元素定位方式

2. **网络要求**
   - 确保设备能正常访问小红书网站
   - 建议使用稳定的网络连接

3. **权限设置**
   - 首次运行可能需要手动授予一些权限
   - 确保浏览器有访问网络的权限

4. **页面变化**
   - 小红书页面结构可能会更新
   - 如果脚本失效，可能需要调整元素定位逻辑

## 故障排除

### 常见问题

1. **设备连接失败**
   - 检查 USB 调试是否开启
   - 尝试重新连接设备
   - 确认 ADB 工具正常工作

2. **浏览器无法打开**
   - 检查浏览器包名是否正确
   - 确认浏览器已安装在设备上

3. **页面加载失败**
   - 检查网络连接
   - 确认链接地址正确
   - 尝试手动在浏览器中打开链接

4. **评论收集不到**
   - 页面可能还在加载中，增加等待时间
   - 小红书页面结构可能有变化
   - 检查评论过滤条件是否过于严格

## 自定义开发

如果需要修改脚本以适应不同的需求：

1. **修改目标链接**: 在 `config.py` 中更改 `target_url`
2. **调整浏览器**: 修改 `package_name` 为其他浏览器
3. **优化评论识别**: 调整 `is_likely_comment` 方法的逻辑
4. **增加功能**: 在 `XHSAutomation` 类中添加新方法

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。
