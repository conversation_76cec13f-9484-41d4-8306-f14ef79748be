#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址栏修复测试脚本
测试修复后的地址栏点击功能
"""

import time
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

class AddressBarFixTester:
    def __init__(self):
        self.poco = None
        self.browser_package = BROWSER_CONFIG["package_name"]
        
    def connect_device(self):
        """连接设备"""
        try:
            adb = ADB()
            devices = adb.devices()
            
            if not devices:
                print("❌ 未发现任何设备")
                return False
            
            device_id = devices[0][0]
            connect_device(f"Android:///{device_id}")
            print(f"📱 已连接设备: {device_id}")
            
            self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            print("✅ 设备连接成功")
            return True
        except Exception as e:
            print(f"❌ 设备连接失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        try:
            print("🚀 正在打开浏览器...")
            start_app(self.browser_package)
            time.sleep(3)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    
    def test_enhanced_address_bar_click(self):
        """测试增强的地址栏点击功能"""
        print("\n🧪 测试增强的地址栏点击功能...")
        print("=" * 60)
        
        # 导入修复后的方法
        from xhs_automation import XHSAutomation
        
        # 创建自动化实例
        automation = XHSAutomation()
        automation.poco = self.poco
        
        # 测试地址栏点击
        print("🎯 开始测试地址栏点击...")
        success = automation._click_address_bar()
        
        if success:
            print("🎉 地址栏点击测试成功!")
            
            # 测试URL输入
            print("\n⌨️ 测试URL输入...")
            url_success = automation._input_url()
            
            if url_success:
                print("✅ URL输入测试成功!")
                
                # 测试搜索按钮点击
                print("\n🔍 测试搜索按钮点击...")
                search_success = automation._click_search_button()
                
                if search_success:
                    print("✅ 搜索按钮点击测试成功!")
                    return True
                else:
                    print("❌ 搜索按钮点击失败，尝试按回车键...")
                    keyevent("KEYCODE_ENTER")
                    return True
            else:
                print("❌ URL输入测试失败")
                return False
        else:
            print("❌ 地址栏点击测试失败")
            return False
    
    def test_manual_verification(self):
        """手动验证测试"""
        print("\n👤 手动验证测试...")
        print("=" * 60)
        
        print("请按照以下步骤进行手动验证：")
        print("1. 观察浏览器是否已打开")
        print("2. 检查地址栏是否可见")
        print("3. 尝试手动点击地址栏")
        
        manual_result = input("\n地址栏是否可以手动点击？(y/n): ").lower().strip()
        
        if manual_result == 'y':
            print("✅ 手动验证：地址栏可以点击")
            
            # 提供手动输入URL的机会
            manual_input = input("是否要手动输入URL进行测试？(y/n): ").lower().strip()
            if manual_input == 'y':
                print("请手动在地址栏中输入以下URL：")
                print(URL_CONFIG["target_url"])
                input("输入完成后按回车继续...")
                
                manual_nav = input("页面是否成功导航到小红书？(y/n): ").lower().strip()
                if manual_nav == 'y':
                    print("✅ 手动验证：导航成功")
                    return True
                else:
                    print("❌ 手动验证：导航失败")
                    return False
            else:
                return True
        else:
            print("❌ 手动验证：地址栏无法点击")
            return False
    
    def analyze_current_state(self):
        """分析当前浏览器状态"""
        print("\n🔍 分析当前浏览器状态...")
        print("=" * 60)
        
        try:
            # 获取屏幕信息
            screen_info = device().display_info
            print(f"📱 屏幕分辨率: {screen_info['width']} x {screen_info['height']}")
            
            # 分析EditText元素
            edit_texts = self.poco("android.widget.EditText")
            print(f"📝 发现 {len(edit_texts)} 个EditText元素:")
            
            for i, edit_text in enumerate(edit_texts):
                try:
                    resource_id = edit_text.attr("resourceId") if edit_text.attr("resourceId") else "无ID"
                    text_content = edit_text.get_text() if edit_text.get_text() else "无文本"
                    pos = edit_text.get_position()
                    size = edit_text.get_size()
                    
                    print(f"  {i+1}. ID: {resource_id}")
                    print(f"     文本: {text_content}")
                    print(f"     位置: ({pos[0]:.0f}, {pos[1]:.0f})")
                    print(f"     大小: {size[0]:.0f} x {size[1]:.0f}")
                    
                    # 判断是否可能是地址栏
                    if pos[1] < screen_info['height'] * 0.3:
                        print(f"     ⭐ 可能是地址栏（位于屏幕上方）")
                    print()
                    
                except Exception as e:
                    print(f"  {i+1}. 分析失败: {e}")
            
            # 分析可点击元素
            clickable_elements = self.poco(clickable=True)
            upper_clickable = []
            
            for element in clickable_elements:
                try:
                    pos = element.get_position()
                    if pos[1] < screen_info['height'] * 0.25:
                        resource_id = element.attr("resourceId") if element.attr("resourceId") else "无ID"
                        text_content = element.get_text() if element.get_text() else "无文本"
                        upper_clickable.append({
                            "id": resource_id,
                            "text": text_content,
                            "pos": pos
                        })
                except:
                    continue
            
            print(f"🔘 屏幕上方可点击元素: {len(upper_clickable)} 个")
            for i, elem in enumerate(upper_clickable[:5]):  # 只显示前5个
                print(f"  {i+1}. ID: {elem['id']}")
                print(f"     文本: {elem['text']}")
                print(f"     位置: ({elem['pos'][0]:.0f}, {elem['pos'][1]:.0f})")
                print()
            
        except Exception as e:
            print(f"❌ 状态分析失败: {e}")
    
    def run_test(self):
        """运行完整测试"""
        print("🧪 地址栏修复测试工具")
        print("=" * 50)
        
        if not self.connect_device():
            return False
        
        if not self.open_browser():
            return False
        
        print("\n⏳ 等待浏览器加载...")
        time.sleep(3)
        
        # 分析当前状态
        self.analyze_current_state()
        
        # 测试增强的地址栏点击功能
        auto_success = self.test_enhanced_address_bar_click()
        
        # 手动验证
        manual_success = self.test_manual_verification()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print("=" * 60)
        
        print(f"🤖 自动化测试: {'✅ 成功' if auto_success else '❌ 失败'}")
        print(f"👤 手动验证: {'✅ 成功' if manual_success else '❌ 失败'}")
        
        if auto_success and manual_success:
            print("\n🎉 地址栏修复成功！")
            print("💡 建议:")
            print("   1. 现在可以运行完整的自动化脚本")
            print("   2. 地址栏点击功能已得到显著改善")
        elif auto_success:
            print("\n✅ 自动化功能正常，但需要确认手动操作")
            print("💡 建议:")
            print("   1. 检查浏览器设置")
            print("   2. 确认网络连接正常")
        elif manual_success:
            print("\n⚠️ 手动操作正常，但自动化需要进一步调整")
            print("💡 建议:")
            print("   1. 根据上面的状态分析调整选择器")
            print("   2. 可能需要针对特定浏览器进行优化")
        else:
            print("\n❌ 测试失败，需要进一步诊断")
            print("💡 建议:")
            print("   1. 检查浏览器是否正确安装")
            print("   2. 尝试使用其他浏览器")
            print("   3. 检查设备权限设置")
        
        return auto_success or manual_success

def main():
    tester = AddressBarFixTester()
    tester.run_test()

if __name__ == "__main__":
    main()
