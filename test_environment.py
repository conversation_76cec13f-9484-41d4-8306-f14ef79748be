#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
用于验证 Airtest 环境是否正确配置
"""

import sys
import time

def test_imports():
    """测试必要的模块导入"""
    print("🔍 测试模块导入...")

    try:
        import airtest.core.api
        print("✅ airtest.core.api 导入成功")
    except ImportError as e:
        print(f"❌ airtest.core.api 导入失败: {e}")
        return False

    try:
        from poco.drivers.android.uiautomation import AndroidUiautomationPoco
        print("✅ poco.drivers.android.uiautomation 导入成功")
    except ImportError as e:
        print(f"❌ poco.drivers.android.uiautomation 导入失败: {e}")
        return False

    return True

def test_adb_connection():
    """测试 ADB 连接"""
    print("\n🔍 测试 ADB 连接...")

    try:
        from airtest.core.android.adb import ADB
        adb = ADB()
        devices = adb.devices()

        if not devices:
            print("❌ 未发现任何设备，请检查:")
            print("   1. 设备是否通过 USB 连接")
            print("   2. 是否开启了开发者选项")
            print("   3. 是否开启了 USB 调试")
            return False

        print(f"✅ 发现 {len(devices)} 个设备:")
        for device in devices:
            print(f"   📱 {device}")

        return True

    except Exception as e:
        print(f"❌ ADB 连接测试失败: {e}")
        return False

def test_device_connection():
    """测试设备连接"""
    print("\n🔍 测试设备连接...")

    try:
        from airtest.core.api import auto_setup, device
        # 尝试自动连接设备
        auto_setup(__file__)
        print("✅ 设备连接成功")

        # 获取设备信息
        device_info = device().display_info
        print(f"📱 设备分辨率: {device_info['width']} x {device_info['height']}")

        return True

    except Exception as e:
        print(f"❌ 设备连接失败: {e}")
        return False

def test_poco_initialization():
    """测试 Poco 初始化"""
    print("\n🔍 测试 Poco 初始化...")

    try:
        from poco.drivers.android.uiautomation import AndroidUiautomationPoco
        poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
        print("✅ Poco 初始化成功")

        # 测试基本操作
        try:
            # 获取屏幕上的元素数量
            elements = poco(textMatches=".*")
            print(f"📊 当前屏幕发现 {len(elements)} 个文本元素")
        except:
            print("⚠️ 无法获取屏幕元素，但 Poco 初始化正常")

        return True

    except Exception as e:
        print(f"❌ Poco 初始化失败: {e}")
        return False

def test_browser_package():
    """测试浏览器包是否存在"""
    print("\n🔍 测试浏览器包...")

    try:
        browser_package = "com.android.browser"

        # 尝试启动浏览器（不实际启动，只检查包是否存在）
        from airtest.core.android.adb import ADB
        adb = ADB()

        # 检查包是否安装
        result = adb.shell(f"pm list packages | grep {browser_package}")

        if browser_package in result:
            print(f"✅ 浏览器包 {browser_package} 已安装")
            return True
        else:
            print(f"❌ 浏览器包 {browser_package} 未找到")
            print("💡 建议:")
            print("   1. 检查设备是否安装了默认浏览器")
            print("   2. 或修改 config.py 中的浏览器包名")
            return False

    except Exception as e:
        print(f"❌ 浏览器包检测失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Airtest 环境测试")
    print("=" * 50)

    tests = [
        ("模块导入", test_imports),
        ("ADB 连接", test_adb_connection),
        ("设备连接", test_device_connection),
        ("Poco 初始化", test_poco_initialization),
        ("浏览器包检测", test_browser_package),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🔬 执行测试: {test_name}")
        print("-" * 30)

        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 所有测试通过！环境配置正确，可以运行主脚本。")
        print("\n💡 下一步:")
        print("   运行: python3 xhs_automation.py")
    else:
        print("⚠️ 部分测试失败，请根据上述提示解决问题后重试。")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
