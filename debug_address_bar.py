#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址栏调试工具
专门用于调试和解决地址栏点击问题
"""

import time
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

class AddressBarDebugger:
    def __init__(self):
        self.poco = None
        self.browser_package = BROWSER_CONFIG["package_name"]
        
    def connect_device(self):
        """连接设备"""
        try:
            adb = ADB()
            devices = adb.devices()
            
            if not devices:
                print("❌ 未发现任何设备")
                return False
            
            device_id = devices[0][0]
            connect_device(f"Android:///{device_id}")
            print(f"📱 已连接设备: {device_id}")
            
            self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            print("✅ 设备连接成功")
            return True
        except Exception as e:
            print(f"❌ 设备连接失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        try:
            print("🚀 正在打开浏览器...")
            start_app(self.browser_package)
            time.sleep(3)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    
    def analyze_address_bar_elements(self):
        """分析地址栏相关元素"""
        print("\n🔍 开始分析地址栏元素...")
        print("=" * 60)
        
        try:
            # 获取所有元素
            all_elements = self.poco("*")
            print(f"📊 总共发现 {len(all_elements)} 个界面元素")
            
            # 分析EditText元素（地址栏通常是EditText）
            print("\n📝 EditText元素分析:")
            print("-" * 40)
            
            edit_texts = self.poco("android.widget.EditText")
            print(f"发现 {len(edit_texts)} 个EditText元素:")
            
            for i, edit_text in enumerate(edit_texts):
                try:
                    resource_id = edit_text.attr("resourceId") if edit_text.attr("resourceId") else "无"
                    text_content = edit_text.get_text() if edit_text.get_text() else "无"
                    pos = edit_text.get_position()
                    size = edit_text.get_size()
                    
                    print(f"\n  📍 EditText {i+1}:")
                    print(f"     ResourceId: {resource_id}")
                    print(f"     文本内容: {text_content}")
                    print(f"     位置: ({pos[0]:.0f}, {pos[1]:.0f})")
                    print(f"     大小: {size[0]:.0f} x {size[1]:.0f}")
                    
                    # 判断是否可能是地址栏
                    screen_height = device().display_info['height']
                    if pos[1] < screen_height * 0.3:
                        print(f"     ⭐ 可能是地址栏（位于屏幕上方）")
                        
                except Exception as e:
                    print(f"     ❌ 分析EditText {i+1} 失败: {e}")
            
            # 分析可能的地址栏关键词
            print("\n🎯 地址栏关键词搜索:")
            print("-" * 40)
            
            keywords = ["url", "address", "search", "omnibox", "location", "搜索", "地址", "网址"]
            found_elements = []
            
            for keyword in keywords:
                print(f"\n🔍 搜索关键词: {keyword}")
                
                # 通过ResourceId搜索
                try:
                    elements_by_id = self.poco(resourceId=f"*{keyword}*")
                    for element in elements_by_id:
                        try:
                            resource_id = element.attr("resourceId")
                            print(f"  📍 ResourceId匹配: {resource_id}")
                            found_elements.append(("ResourceId", resource_id, keyword))
                        except:
                            pass
                except:
                    pass
                
                # 通过文本搜索
                try:
                    elements_by_text = self.poco(textMatches=f".*{keyword}.*")
                    for element in elements_by_text:
                        try:
                            text_content = element.get_text()
                            print(f"  📝 文本匹配: {text_content}")
                            found_elements.append(("Text", text_content, keyword))
                        except:
                            pass
                except:
                    pass
            
            # 分析屏幕上方的可点击元素
            print("\n📍 屏幕上方可点击元素:")
            print("-" * 40)
            
            screen_width = device().display_info['width']
            screen_height = device().display_info['height']
            
            clickable_elements = self.poco(clickable=True)
            upper_elements = []
            
            for element in clickable_elements:
                try:
                    pos = element.get_position()
                    if pos[1] < screen_height * 0.25:  # 屏幕上方25%区域
                        resource_id = element.attr("resourceId") if element.attr("resourceId") else "无"
                        text_content = element.get_text() if element.get_text() else "无"
                        class_name = element.attr("className") if element.attr("className") else "无"
                        
                        upper_elements.append({
                            "resourceId": resource_id,
                            "text": text_content,
                            "className": class_name,
                            "position": pos
                        })
                except:
                    continue
            
            print(f"发现 {len(upper_elements)} 个屏幕上方的可点击元素:")
            for i, elem in enumerate(upper_elements[:10]):  # 只显示前10个
                print(f"\n  🔘 元素 {i+1}:")
                print(f"     ResourceId: {elem['resourceId']}")
                print(f"     文本: {elem['text']}")
                print(f"     类名: {elem['className']}")
                print(f"     位置: ({elem['position'][0]:.0f}, {elem['position'][1]:.0f})")
            
            return found_elements
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return []
    
    def test_address_bar_clicks(self):
        """测试地址栏点击"""
        print("\n🧪 开始测试地址栏点击...")
        print("=" * 60)
        
        # 测试方法1：ResourceId
        print("\n📍 测试方法1: ResourceId选择器")
        selectors = [
            "com.android.browser:id/url",
            "android:id/url",
            "url",
            "address_bar",
            "omnibox",
            "com.android.browser:id/location_bar",
            "com.android.browser:id/url_bar",
            "com.android.browser:id/addressbar",
            "com.android.browser:id/search_box"
        ]
        
        for selector in selectors:
            try:
                if self.poco(resourceId=selector).exists():
                    print(f"✅ 找到: {selector}")
                    print("   尝试点击...")
                    self.poco(resourceId=selector).click()
                    time.sleep(2)
                    
                    if self._check_input_active():
                        print("   🎉 点击成功！地址栏已激活")
                        return True
                    else:
                        print("   ❌ 点击后地址栏未激活")
                else:
                    print(f"❌ 未找到: {selector}")
            except Exception as e:
                print(f"❌ 测试 {selector} 失败: {e}")
        
        # 测试方法2：文本选择器
        print("\n📍 测试方法2: 文本选择器")
        texts = [
            "搜索或输入网址", "输入网址", "地址栏", "搜索栏",
            "Search or type URL", "Search", "搜索"
        ]
        
        for text in texts:
            try:
                if self.poco(text=text).exists():
                    print(f"✅ 找到文本: {text}")
                    print("   尝试点击...")
                    self.poco(text=text).click()
                    time.sleep(2)
                    
                    if self._check_input_active():
                        print("   🎉 点击成功！地址栏已激活")
                        return True
                    else:
                        print("   ❌ 点击后地址栏未激活")
                else:
                    print(f"❌ 未找到文本: {text}")
            except Exception as e:
                print(f"❌ 测试文本 {text} 失败: {e}")
        
        # 测试方法3：EditText点击
        print("\n📍 测试方法3: EditText元素点击")
        try:
            edit_texts = self.poco("android.widget.EditText")
            for i, edit_text in enumerate(edit_texts):
                try:
                    pos = edit_text.get_position()
                    screen_height = device().display_info['height']
                    
                    if pos[1] < screen_height * 0.3:
                        print(f"✅ 尝试点击EditText {i+1}")
                        edit_text.click()
                        time.sleep(2)
                        
                        if self._check_input_active():
                            print("   🎉 点击成功！地址栏已激活")
                            return True
                        else:
                            print("   ❌ 点击后地址栏未激活")
                except:
                    continue
        except:
            pass
        
        # 测试方法4：位置点击
        print("\n📍 测试方法4: 位置点击")
        screen_width = device().display_info['width']
        screen_height = device().display_info['height']
        
        positions = [
            (screen_width // 2, screen_height // 10),
            (screen_width // 2, screen_height // 8),
            (screen_width // 2, screen_height // 6),
        ]
        
        for i, pos in enumerate(positions):
            print(f"✅ 尝试点击位置 {i+1}: ({pos[0]}, {pos[1]})")
            touch(pos)
            time.sleep(2)
            
            if self._check_input_active():
                print("   🎉 点击成功！地址栏已激活")
                return True
            else:
                print("   ❌ 点击后地址栏未激活")
        
        print("❌ 所有测试方法都失败了")
        return False
    
    def _check_input_active(self):
        """检查输入框是否已激活"""
        try:
            # 方法1：检查键盘是否出现
            keyboard_keys = ["q", "w", "e", "r", "t", "y", "1", "2", "3", "0"]
            for key in keyboard_keys:
                try:
                    if self.poco(text=key).exists():
                        print("     ✓ 检测到键盘")
                        return True
                except:
                    continue
            
            # 方法2：检查光标或输入提示
            input_indicators = ["输入", "搜索", "cursor", "|"]
            for indicator in input_indicators:
                try:
                    if self.poco(textMatches=f".*{indicator}.*").exists():
                        print(f"     ✓ 检测到输入指示器: {indicator}")
                        return True
                except:
                    continue
            
            print("     ❌ 未检测到输入激活状态")
            return False
        except:
            return False
    
    def run_debug(self):
        """运行调试"""
        print("🐛 地址栏调试工具")
        print("=" * 50)
        
        if not self.connect_device():
            return False
        
        if not self.open_browser():
            return False
        
        print("\n⏳ 等待浏览器加载...")
        time.sleep(3)
        
        # 分析地址栏元素
        found_elements = self.analyze_address_bar_elements()
        
        # 测试地址栏点击
        success = self.test_address_bar_clicks()
        
        print("\n" + "=" * 60)
        print("📊 调试结果总结:")
        print("=" * 60)
        
        if success:
            print("🎉 成功找到并激活了地址栏！")
        else:
            print("❌ 未能成功激活地址栏")
        
        print(f"\n💡 发现的可能地址栏元素: {len(found_elements)} 个")
        for elem_type, elem_value, keyword in found_elements[:5]:
            print(f"   - {elem_type}: {elem_value} (关键词: {keyword})")
        
        print("\n🔧 建议:")
        if success:
            print("   1. 地址栏点击功能正常")
            print("   2. 可以继续运行完整的自动化脚本")
        else:
            print("   1. 尝试手动点击地址栏")
            print("   2. 检查浏览器是否完全加载")
            print("   3. 尝试使用其他浏览器")
            print("   4. 根据上面的分析结果更新选择器")
        
        return success

def main():
    debugger = AddressBarDebugger()
    debugger.run_debug()

if __name__ == "__main__":
    main()
