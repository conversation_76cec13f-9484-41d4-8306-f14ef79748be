# 小红书自动化评论收集 - 使用指南

## 📋 项目概述

这是一个使用 Airtest 框架开发的自动化脚本，可以在 Android 设备上自动打开浏览器，导航到指定的小红书帖子，并收集评论内容。

## 🎯 功能特性

- 🤖 自动连接 Android 设备
- 🌐 自动打开浏览器并导航到小红书链接
- 🔐 自动处理权限弹窗
- 📜 自动滑动页面收集评论
- 💬 智能过滤评论内容
- 📊 在控制台输出收集结果

## 📁 项目文件

```
xhs/
├── xhs_automation.py    # 主要的自动化脚本
├── config.py           # 配置文件
├── demo.py             # 演示脚本
├── test_environment.py # 环境测试脚本
├── debug_browser.py    # 浏览器调试脚本
├── test_navigation.py  # 导航测试脚本
├── README.md           # 详细说明文档
└── 使用指南.md         # 本文件
```

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.6+
- 已连接的 Android 设备（开启 USB 调试）

### 2. 安装依赖

```bash
pip install airtest pocoui
```

### 3. 测试环境

```bash
python3 test_environment.py
```

如果所有测试通过，说明环境配置正确。

### 4. 运行演示

```bash
python3 demo.py
```

这将运行一个简化的演示版本，帮助您了解脚本的工作流程。

### 5. 调试浏览器（如果遇到问题）

如果浏览器导航有问题，可以运行调试脚本：

```bash
python3 debug_browser.py
```

这会分析浏览器界面元素，帮助找到正确的地址栏和搜索按钮。

### 6. 测试导航功能

```bash
python3 test_navigation.py
```

这会分步测试导航过程，帮助定位问题。

### 7. 运行完整脚本

```bash
python3 xhs_automation.py
```

## ⚙️ 配置说明

您可以通过修改 `config.py` 文件来调整脚本行为：

### 设备配置
```python
DEVICE_CONFIG = {
    "device_uri": None,  # 设备URI，None表示自动连接
    "connect_timeout": 30,
}
```

### 浏览器配置
```python
BROWSER_CONFIG = {
    "package_name": "com.android.browser",  # 浏览器包名
    "startup_wait": 3,
}
```

### URL配置
```python
URL_CONFIG = {
    "target_url": "https://www.xiaohongshu.com/...",  # 目标链接
    "page_load_wait": 5,
    "page_load_timeout": 30,
}
```

### 滑动配置
```python
SCROLL_CONFIG = {
    "max_scrolls": 5,      # 最大滑动次数
    "scroll_wait": 2,      # 滑动后等待时间
    "scroll_duration": 1,  # 滑动持续时间
}
```

## 🔧 常见问题

### Q: 设备连接失败
**A:** 请检查：
- USB 调试是否开启
- 设备是否通过 USB 连接
- 是否信任了电脑

### Q: 浏览器无法打开
**A:** 请检查：
- 设备是否安装了默认浏览器
- 浏览器包名是否正确
- 可以尝试修改 `config.py` 中的包名

### Q: 浏览器未点击到搜索按钮
**A:** 这是常见问题，解决方法：
1. 运行 `python3 debug_browser.py` 分析浏览器界面
2. 运行 `python3 test_navigation.py` 分步测试导航
3. 查看控制台输出，找到正确的元素选择器
4. 手动测试地址栏和搜索按钮的点击
5. 如果仍然失败，脚本会自动尝试按回车键

### Q: 未点击到浏览器的搜索栏/地址栏
**A:** 已修复！新版本包含6轮地址栏识别策略：
1. 运行 `python3 test_address_bar_fix.py` 测试修复效果
2. 新增功能：
   - 12种ResourceId选择器
   - 8种文本匹配方式
   - 部分文本匹配
   - EditText类型智能识别
   - 8个位置点击策略
   - 双击和长按尝试
   - 实时验证激活状态
3. 如果仍有问题，脚本会提供详细的调试信息

### Q: 页面加载失败
**A:** 请检查：
- 网络连接是否正常
- 小红书链接是否有效
- 可以增加 `page_load_timeout` 的值

### Q: 收集不到评论
**A:** 可能的原因：
- 页面还在加载中
- 评论区域需要滑动才能显示
- 评论过滤条件过于严格

## 🎨 自定义开发

### 修改目标链接
在 `config.py` 中修改 `URL_CONFIG["target_url"]`

### 使用其他浏览器
在 `config.py` 中修改 `BROWSER_CONFIG["package_name"]`，常见的浏览器包名：
- Chrome: `com.android.chrome`
- Firefox: `org.mozilla.firefox`
- UC浏览器: `com.UCMobile.intl`

### 调整评论识别
修改 `xhs_automation.py` 中的 `is_likely_comment` 方法

### 增加新功能
在 `XHSAutomation` 类中添加新方法

## 📝 输出示例

```
🎯 小红书评论收集自动化脚本
目标链接: https://www.xiaohongshu.com/discovery/item/640745c5000000001303ed88
浏览器包名: com.android.browser
------------------------------------------------------------
🤖 开始执行小红书自动化任务...
✅ 设备连接成功
🚀 正在打开浏览器...
✅ 浏览器已打开
🔍 正在导航到小红书链接...
✅ 已导航到目标链接
🔐 正在处理权限弹窗...
✅ 已点击 '允许' 按钮
⏳ 等待小红书页面加载...
✅ 小红书页面已加载
📜 开始滑动页面收集评论...
🔄 第 1 次滑动...
🔄 第 2 次滑动...
...
✅ 滑动完成，共收集到 15 条评论

==================================================
📝 收集到的评论内容:
==================================================

💬 评论 1:
   这个真的太好看了！

💬 评论 2:
   请问在哪里买的呀？

...

📊 总计: 15 条评论
==================================================
✅ 自动化任务执行完成!
```

## ⚠️ 注意事项

1. **合法使用**: 请遵守小红书的使用条款和相关法律法规
2. **频率控制**: 避免过于频繁的自动化操作
3. **设备安全**: 确保设备安全，避免恶意软件
4. **数据隐私**: 注意保护个人隐私和数据安全

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 首先运行 `python3 test_environment.py` 检查环境
2. 查看控制台输出的错误信息
3. 检查设备连接和网络状态
4. 参考本文档的常见问题部分

## 📄 许可证

本项目仅供学习和研究使用。
