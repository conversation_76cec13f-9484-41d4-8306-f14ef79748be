#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导航测试脚本
专门测试浏览器导航功能，包括地址栏点击和搜索按钮点击
"""

import time
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

class NavigationTester:
    def __init__(self):
        self.poco = None
        self.browser_package = BROWSER_CONFIG["package_name"]
        self.target_url = URL_CONFIG["target_url"]
        
    def connect_device(self):
        """连接设备"""
        try:
            # 手动连接第一个可用设备
            adb = ADB()
            devices = adb.devices()
            
            if not devices:
                print("❌ 未发现任何设备")
                return False
            
            # 连接第一个设备
            device_id = devices[0][0]
            connect_device(f"Android:///{device_id}")
            print(f"📱 已连接设备: {device_id}")
            
            # 初始化 Poco
            self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            print("✅ 设备连接成功")
            return True
        except Exception as e:
            print(f"❌ 设备连接失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        try:
            print("🚀 正在打开浏览器...")
            start_app(self.browser_package)
            time.sleep(3)
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False
    
    def test_step_by_step_navigation(self):
        """分步测试导航过程"""
        print("\n🧪 开始分步测试导航...")
        print("=" * 50)
        
        # 步骤1：查找并点击地址栏
        print("\n📍 步骤1: 查找并点击地址栏")
        print("-" * 30)
        
        if self._test_click_address_bar():
            print("✅ 地址栏点击成功")
        else:
            print("❌ 地址栏点击失败")
            return False
        
        # 等待用户确认
        input("\n⏸️  请检查地址栏是否已激活（光标是否出现），然后按回车继续...")
        
        # 步骤2：输入URL
        print("\n⌨️  步骤2: 输入URL")
        print("-" * 30)
        
        if self._test_input_url():
            print("✅ URL输入成功")
        else:
            print("❌ URL输入失败")
            return False
        
        # 等待用户确认
        input("\n⏸️  请检查URL是否已正确输入，然后按回车继续...")
        
        # 步骤3：查找并点击搜索按钮
        print("\n🔍 步骤3: 查找并点击搜索按钮")
        print("-" * 30)
        
        if self._test_click_search_button():
            print("✅ 搜索按钮点击成功")
        else:
            print("❌ 搜索按钮点击失败，尝试按回车键...")
            keyevent("KEYCODE_ENTER")
        
        # 等待页面加载
        print("\n⏳ 等待页面加载...")
        time.sleep(5)
        
        return True
    
    def _test_click_address_bar(self):
        """测试点击地址栏"""
        # 尝试多种地址栏选择器
        selectors = [
            "com.android.browser:id/url",
            "android:id/url",
            "url",
            "address_bar",
            "omnibox",
            "com.android.browser:id/location_bar",
            "com.android.browser:id/url_bar"
        ]
        
        print("🎯 尝试通过ResourceId查找地址栏...")
        for selector in selectors:
            try:
                if self.poco(resourceId=selector).exists():
                    print(f"  ✅ 找到地址栏: {selector}")
                    self.poco(resourceId=selector).click()
                    time.sleep(1)
                    return True
                else:
                    print(f"  ❌ 未找到: {selector}")
            except Exception as e:
                print(f"  ❌ 测试 {selector} 失败: {e}")
        
        print("🎯 尝试通过文本查找地址栏...")
        texts = ["搜索或输入网址", "输入网址", "地址栏", "Search or type URL", "搜索"]
        for text in texts:
            try:
                if self.poco(text=text).exists():
                    print(f"  ✅ 通过文本找到地址栏: {text}")
                    self.poco(text=text).click()
                    time.sleep(1)
                    return True
                else:
                    print(f"  ❌ 未找到文本: {text}")
            except Exception as e:
                print(f"  ❌ 测试文本 {text} 失败: {e}")
        
        print("🎯 尝试点击屏幕上方区域...")
        try:
            screen_width = device().display_info['width']
            screen_height = device().display_info['height']
            touch((screen_width // 2, screen_height // 10))
            time.sleep(1)
            print("  ✅ 已点击屏幕上方区域")
            return True
        except Exception as e:
            print(f"  ❌ 点击屏幕上方区域失败: {e}")
        
        return False
    
    def _test_input_url(self):
        """测试输入URL"""
        try:
            print(f"⌨️  正在输入URL: {self.target_url}")
            
            # 清空当前内容
            keyevent("KEYCODE_CTRL_A")  # 全选
            time.sleep(0.5)
            keyevent("KEYCODE_DEL")     # 删除
            time.sleep(0.5)
            
            # 输入URL
            text(self.target_url)
            time.sleep(1)
            print("✅ URL输入完成")
            return True
            
        except Exception as e:
            print(f"❌ URL输入失败: {e}")
            return False
    
    def _test_click_search_button(self):
        """测试点击搜索按钮"""
        print("🔍 尝试通过ResourceId查找搜索按钮...")
        selectors = [
            "com.android.browser:id/go_button",
            "com.android.browser:id/search_button",
            "com.android.browser:id/url_bar_go_button",
            "go_button",
            "search_button",
            "navigate_button",
            "android:id/button1"
        ]
        
        for selector in selectors:
            try:
                if self.poco(resourceId=selector).exists():
                    print(f"  ✅ 找到搜索按钮: {selector}")
                    self.poco(resourceId=selector).click()
                    time.sleep(1)
                    return True
                else:
                    print(f"  ❌ 未找到: {selector}")
            except Exception as e:
                print(f"  ❌ 测试 {selector} 失败: {e}")
        
        print("🔍 尝试通过文本查找搜索按钮...")
        texts = ["前往", "搜索", "Go", "Search", "→", "➤", "确定", "OK"]
        for text in texts:
            try:
                if self.poco(text=text).exists():
                    print(f"  ✅ 通过文本找到搜索按钮: {text}")
                    self.poco(text=text).click()
                    time.sleep(1)
                    return True
                else:
                    print(f"  ❌ 未找到文本: {text}")
            except Exception as e:
                print(f"  ❌ 测试文本 {text} 失败: {e}")
        
        print("🔍 尝试查找图标按钮...")
        try:
            image_buttons = self.poco("android.widget.ImageButton")
            for i, button in enumerate(image_buttons):
                try:
                    button_pos = button.get_position()
                    screen_width = device().display_info['width']
                    
                    if button_pos[0] > screen_width * 0.7:
                        print(f"  ✅ 找到可能的搜索图标按钮 {i+1}")
                        button.click()
                        time.sleep(1)
                        return True
                except:
                    continue
        except:
            pass
        
        return False
    
    def run_test(self):
        """运行测试"""
        print("🧪 浏览器导航测试工具")
        print("=" * 50)
        
        if not self.connect_device():
            return False
        
        if not self.open_browser():
            return False
        
        # 等待浏览器完全加载
        print("\n⏳ 等待浏览器加载...")
        time.sleep(3)
        
        # 分步测试导航
        success = self.test_step_by_step_navigation()
        
        if success:
            print("\n✅ 导航测试完成!")
        else:
            print("\n❌ 导航测试失败!")
        
        print("\n💡 建议:")
        print("   1. 如果地址栏点击失败，请手动点击地址栏")
        print("   2. 如果搜索按钮点击失败，请手动点击搜索按钮或按回车键")
        print("   3. 观察控制台输出，找到正确的元素选择器")
        print("   4. 更新 xhs_automation.py 中的选择器")
        
        return success

def main():
    tester = NavigationTester()
    tester.run_test()

if __name__ == "__main__":
    main()
