#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书自动化演示脚本
简化版本，用于测试基本功能
"""

from xhs_automation import XHSAutomation

def main():
    """演示主函数"""
    print("🎯 小红书自动化演示")
    print("=" * 50)
    
    # 创建自动化实例
    automation = XHSAutomation()
    
    print("📱 正在连接设备...")
    if not automation.connect_device():
        print("❌ 设备连接失败，请检查:")
        print("   1. 设备是否通过 USB 连接")
        print("   2. 是否开启了开发者选项")
        print("   3. 是否开启了 USB 调试")
        return False
    
    print("\n🚀 正在打开浏览器...")
    if not automation.open_browser():
        print("❌ 浏览器打开失败，请检查:")
        print("   1. 设备是否安装了默认浏览器")
        print("   2. 浏览器包名是否正确")
        return False
    
    print("\n🔍 正在导航到小红书链接...")
    if not automation.navigate_to_url():
        print("❌ 导航失败，请检查:")
        print("   1. 网络连接是否正常")
        print("   2. 链接是否有效")
        return False
    
    print("\n🔐 正在处理权限弹窗...")
    automation.handle_permissions()
    
    print("\n⏳ 等待页面加载...")
    automation.wait_for_xhs_page()
    
    print("\n📜 开始收集评论...")
    if automation.scroll_and_collect_comments(max_scrolls=3):  # 减少滑动次数用于演示
        automation.print_comments()
    else:
        print("❌ 评论收集失败")
    
    print("\n✅ 演示完成!")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
