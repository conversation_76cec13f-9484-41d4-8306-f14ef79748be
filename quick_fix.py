#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本
专门解决浏览器搜索按钮点击问题的简化版本
"""

import time
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

def connect_device():
    """连接设备"""
    try:
        adb = ADB()
        devices = adb.devices()
        
        if not devices:
            print("❌ 未发现任何设备")
            return False, None
        
        device_id = devices[0][0]
        connect_device(f"Android:///{device_id}")
        print(f"📱 已连接设备: {device_id}")
        
        poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
        print("✅ 设备连接成功")
        return True, poco
    except Exception as e:
        print(f"❌ 设备连接失败: {e}")
        return False, None

def open_browser():
    """打开浏览器"""
    try:
        print("🚀 正在打开浏览器...")
        start_app(BROWSER_CONFIG["package_name"])
        time.sleep(3)
        print("✅ 浏览器已打开")
        return True
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        return False

def simple_navigation(poco):
    """简化的导航方法"""
    print("\n🔍 开始简化导航...")
    
    target_url = URL_CONFIG["target_url"]
    
    # 方法1：直接点击屏幕上方并输入
    print("📍 方法1: 点击屏幕上方区域...")
    try:
        screen_width = device().display_info['width']
        screen_height = device().display_info['height']
        
        # 点击屏幕上方中央（地址栏位置）
        touch((screen_width // 2, screen_height // 8))
        time.sleep(2)
        
        # 清空并输入URL
        keyevent("KEYCODE_CTRL_A")
        time.sleep(0.5)
        keyevent("KEYCODE_DEL")
        time.sleep(0.5)
        
        text(target_url)
        time.sleep(2)
        
        print("✅ URL已输入，尝试按回车键...")
        keyevent("KEYCODE_ENTER")
        time.sleep(5)
        
        # 检查是否成功导航
        if check_navigation_success(poco):
            print("✅ 导航成功!")
            return True
            
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2：尝试查找并点击任何可能的搜索按钮
    print("\n📍 方法2: 查找搜索按钮...")
    try:
        # 查找所有可能的搜索按钮文本
        search_texts = ["前往", "Go", "搜索", "Search", "确定", "OK", "→", "➤"]
        
        for text_to_find in search_texts:
            try:
                if poco(text=text_to_find).exists():
                    print(f"✅ 找到按钮: {text_to_find}")
                    poco(text=text_to_find).click()
                    time.sleep(3)
                    
                    if check_navigation_success(poco):
                        print("✅ 导航成功!")
                        return True
            except:
                continue
                
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3：查找图标按钮
    print("\n📍 方法3: 查找图标按钮...")
    try:
        # 查找所有ImageButton
        image_buttons = poco("android.widget.ImageButton")
        
        for i, button in enumerate(image_buttons):
            try:
                print(f"尝试点击图标按钮 {i+1}...")
                button.click()
                time.sleep(3)
                
                if check_navigation_success(poco):
                    print("✅ 导航成功!")
                    return True
                    
            except:
                continue
                
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    # 方法4：多次尝试回车键
    print("\n📍 方法4: 多次尝试回车键...")
    try:
        for i in range(3):
            print(f"第 {i+1} 次按回车键...")
            keyevent("KEYCODE_ENTER")
            time.sleep(3)
            
            if check_navigation_success(poco):
                print("✅ 导航成功!")
                return True
                
    except Exception as e:
        print(f"❌ 方法4失败: {e}")
    
    print("❌ 所有导航方法都失败了")
    return False

def check_navigation_success(poco):
    """检查导航是否成功"""
    try:
        # 检查是否有小红书相关内容
        success_indicators = [
            "小红书",
            "xiaohongshu",
            "评论",
            "点赞",
            "收藏",
            "分享"
        ]
        
        for indicator in success_indicators:
            try:
                if poco(textMatches=f".*{indicator}.*").exists():
                    print(f"✅ 检测到成功指标: {indicator}")
                    return True
            except:
                continue
        
        return False
        
    except:
        return False

def main():
    """主函数"""
    print("🔧 浏览器搜索按钮快速修复工具")
    print("=" * 50)
    
    # 连接设备
    success, poco = connect_device()
    if not success:
        return
    
    # 打开浏览器
    if not open_browser():
        return
    
    # 等待浏览器加载
    print("\n⏳ 等待浏览器加载...")
    time.sleep(3)
    
    # 尝试导航
    if simple_navigation(poco):
        print("\n🎉 修复成功! 浏览器已成功导航到小红书页面")
        print("\n💡 现在您可以:")
        print("   1. 运行完整的自动化脚本")
        print("   2. 手动继续浏览和收集评论")
    else:
        print("\n❌ 修复失败，建议:")
        print("   1. 手动在浏览器中输入链接")
        print("   2. 检查网络连接")
        print("   3. 尝试使用其他浏览器")
        print("   4. 运行 debug_browser.py 进行详细分析")

if __name__ == "__main__":
    main()
