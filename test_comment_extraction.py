#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评论提取测试脚本
测试修复后的评论提取功能，确保只从"共有X条评论"下方提取真正的评论内容
"""

import time
import re
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

class CommentExtractionTester:
    def __init__(self):
        self.poco = None
        self.browser_package = BROWSER_CONFIG["package_name"]
        
    def connect_device(self):
        """连接设备"""
        try:
            adb = ADB()
            devices = adb.devices()
            
            if not devices:
                print("❌ 未发现任何设备")
                return False
            
            device_id = devices[0][0]
            connect_device(f"Android:///{device_id}")
            print(f"📱 已连接设备: {device_id}")
            
            self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            print("✅ 设备连接成功")
            return True
        except Exception as e:
            print(f"❌ 设备连接失败: {e}")
            return False
    
    def analyze_page_structure(self):
        """分析页面结构，查找评论相关元素"""
        print("\n🔍 分析页面结构...")
        print("=" * 60)
        
        try:
            # 获取所有文本元素
            all_text_elements = self.poco(textMatches=".*")
            
            # 查找评论相关的文本
            comment_related_texts = []
            all_texts = []
            
            for element in all_text_elements:
                try:
                    text_content = element.get_text()
                    if text_content and text_content.strip():
                        text_content = text_content.strip()
                        pos = element.get_position()
                        
                        all_texts.append({
                            'text': text_content,
                            'y_position': pos[1],
                            'x_position': pos[0]
                        })
                        
                        # 检查是否包含评论相关关键词
                        comment_keywords = ["评论", "comment", "共有", "条"]
                        if any(keyword in text_content.lower() for keyword in comment_keywords):
                            comment_related_texts.append({
                                'text': text_content,
                                'y_position': pos[1],
                                'x_position': pos[0]
                            })
                except:
                    continue
            
            # 按Y坐标排序
            all_texts.sort(key=lambda x: x['y_position'])
            comment_related_texts.sort(key=lambda x: x['y_position'])
            
            print(f"📊 页面分析结果:")
            print(f"   - 总文本元素: {len(all_texts)} 个")
            print(f"   - 评论相关文本: {len(comment_related_texts)} 个")
            
            print(f"\n📝 评论相关文本:")
            for i, item in enumerate(comment_related_texts):
                print(f"   {i+1}. '{item['text']}' (Y: {item['y_position']:.0f})")
            
            # 查找"共有X条评论"模式
            print(f"\n🎯 查找评论区域标识:")
            comment_patterns = [
                r"共有\d+条评论",
                r"共\d+条评论", 
                r"\d+条评论",
                r"评论\s*\(\d+\)",
                r"评论\s*\d+"
            ]
            
            found_comment_section = None
            for item in comment_related_texts:
                for pattern in comment_patterns:
                    if re.search(pattern, item['text']):
                        found_comment_section = item
                        print(f"   ✅ 找到: '{item['text']}' (Y: {item['y_position']:.0f})")
                        break
                if found_comment_section:
                    break
            
            if not found_comment_section:
                print("   ❌ 未找到标准的评论区域标识")
            
            return all_texts, comment_related_texts, found_comment_section
            
        except Exception as e:
            print(f"❌ 页面结构分析失败: {e}")
            return [], [], None
    
    def test_comment_extraction(self):
        """测试评论提取功能"""
        print("\n🧪 测试评论提取功能...")
        print("=" * 60)
        
        # 导入修复后的方法
        from xhs_automation import XHSAutomation
        
        # 创建自动化实例
        automation = XHSAutomation()
        automation.poco = self.poco
        
        # 测试评论提取
        print("🔍 开始提取评论...")
        comments = automation.extract_comments_from_current_view()
        
        print(f"\n📊 提取结果:")
        print(f"   - 提取到评论数量: {len(comments)} 条")
        
        if comments:
            print(f"\n📝 提取到的评论内容:")
            for i, comment in enumerate(comments[:10], 1):  # 只显示前10条
                print(f"   {i}. {comment}")
            
            if len(comments) > 10:
                print(f"   ... 还有 {len(comments) - 10} 条评论")
        else:
            print("   ❌ 未提取到任何评论")
        
        return comments
    
    def compare_extraction_methods(self):
        """比较新旧提取方法的效果"""
        print("\n⚖️ 比较提取方法效果...")
        print("=" * 60)
        
        from xhs_automation import XHSAutomation
        automation = XHSAutomation()
        automation.poco = self.poco
        
        # 新方法（修复后）
        print("🆕 使用新方法提取评论...")
        new_comments = automation.extract_comments_from_current_view()
        
        # 旧方法（备用方法）
        print("🔄 使用旧方法提取评论...")
        old_comments = automation._extract_comments_fallback()
        
        print(f"\n📊 对比结果:")
        print(f"   - 新方法提取: {len(new_comments)} 条评论")
        print(f"   - 旧方法提取: {len(old_comments)} 条评论")
        
        # 分析差异
        new_set = set(new_comments)
        old_set = set(old_comments)
        
        only_in_new = new_set - old_set
        only_in_old = old_set - new_set
        common = new_set & old_set
        
        print(f"   - 共同评论: {len(common)} 条")
        print(f"   - 仅新方法有: {len(only_in_new)} 条")
        print(f"   - 仅旧方法有: {len(only_in_old)} 条")
        
        if only_in_new:
            print(f"\n✅ 新方法额外提取的评论:")
            for i, comment in enumerate(list(only_in_new)[:5], 1):
                print(f"   {i}. {comment}")
        
        if only_in_old:
            print(f"\n⚠️ 旧方法额外提取的内容（可能是噪音）:")
            for i, comment in enumerate(list(only_in_old)[:5], 1):
                print(f"   {i}. {comment}")
        
        return new_comments, old_comments
    
    def manual_verification(self):
        """手动验证"""
        print("\n👤 手动验证...")
        print("=" * 60)
        
        print("请手动检查当前页面：")
        print("1. 是否能看到'共有X条评论'这样的文字？")
        print("2. 在该文字下方是否有真正的用户评论？")
        print("3. 评论内容是否合理？")
        
        has_comment_section = input("\n是否能看到评论区域标识（如'共有X条评论'）？(y/n): ").lower().strip()
        
        if has_comment_section == 'y':
            comment_text = input("请输入看到的评论区域标识文字: ").strip()
            print(f"✅ 用户确认看到评论标识: '{comment_text}'")
            
            has_real_comments = input("在该标识下方是否有真正的用户评论？(y/n): ").lower().strip()
            
            if has_real_comments == 'y':
                sample_comment = input("请输入一条看到的真实评论内容: ").strip()
                print(f"✅ 用户确认看到真实评论: '{sample_comment}'")
                return True, comment_text, sample_comment
            else:
                print("❌ 用户确认没有看到真实评论")
                return False, comment_text, None
        else:
            print("❌ 用户确认没有看到评论区域标识")
            return False, None, None
    
    def run_test(self):
        """运行完整测试"""
        print("🧪 评论提取功能测试工具")
        print("=" * 50)
        
        if not self.connect_device():
            return False
        
        print("\n⏳ 请确保已经打开小红书页面，然后按回车继续...")
        input()
        
        # 分析页面结构
        all_texts, comment_texts, comment_section = self.analyze_page_structure()
        
        # 测试评论提取
        extracted_comments = self.test_comment_extraction()
        
        # 比较提取方法
        new_comments, old_comments = self.compare_extraction_methods()
        
        # 手动验证
        manual_result, manual_section, manual_comment = self.manual_verification()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print("=" * 60)
        
        print(f"🔍 页面分析:")
        print(f"   - 找到评论区域标识: {'✅' if comment_section else '❌'}")
        if comment_section:
            print(f"   - 标识内容: '{comment_section['text']}'")
        
        print(f"\n🤖 自动提取:")
        print(f"   - 新方法提取: {len(new_comments)} 条")
        print(f"   - 旧方法提取: {len(old_comments)} 条")
        print(f"   - 提取效果: {'✅ 改进' if len(new_comments) < len(old_comments) else '⚠️ 需要调整'}")
        
        print(f"\n👤 手动验证:")
        print(f"   - 找到评论标识: {'✅' if manual_result else '❌'}")
        if manual_result and manual_comment:
            print(f"   - 样本评论: '{manual_comment}'")
        
        # 综合评估
        if comment_section and len(new_comments) > 0 and manual_result:
            print(f"\n🎉 评论提取功能修复成功！")
            print(f"💡 建议: 现在可以运行完整的自动化脚本")
        elif comment_section and len(new_comments) > 0:
            print(f"\n✅ 自动提取功能正常，建议进行更多测试")
        else:
            print(f"\n⚠️ 需要进一步调整评论提取逻辑")
            print(f"💡 建议: 检查页面是否正确加载，或调整识别模式")
        
        return len(new_comments) > 0

def main():
    tester = CommentExtractionTester()
    tester.run_test()

if __name__ == "__main__":
    main()
