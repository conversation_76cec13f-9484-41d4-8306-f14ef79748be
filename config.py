#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 设备连接配置
DEVICE_CONFIG = {
    # 设备连接URI，如果为None则自动连接第一个设备
    "device_uri": None,

    # 连接超时时间（秒）
    "connect_timeout": 30,
}

# 浏览器配置
BROWSER_CONFIG = {
    # 浏览器包名
    "package_name": "com.android.browser",

    # 启动等待时间（秒）
    "startup_wait": 3,
}

# 目标URL配置
URL_CONFIG = {
    # 小红书帖子链接
    "target_url": "https://www.xiaohongshu.com/discovery/item/640745c5000000001303ed88?xsec_token=AB_8amFkTp06n6YXjSJrsYDDNWPRwZfr1y4YfhhewTg80=&xsec_source=pc_feed",

    # 页面加载等待时间（秒）
    "page_load_wait": 5,

    # 页面加载超时时间（秒）
    "page_load_timeout": 30,
}

# 滑动和评论收集配置
SCROLL_CONFIG = {
    # 最大滑动次数
    "max_scrolls": 5,

    # 每次滑动后等待时间（秒）
    "scroll_wait": 2,

    # 滑动持续时间（秒）
    "scroll_duration": 1,
}

# 评论过滤配置
COMMENT_FILTER = {
    # 最小评论长度
    "min_length": 3,

    # 最大评论长度
    "max_length": 500,

    # 排除关键词（基础界面元素）
    "exclude_keywords": [
        "小红书", "点赞", "收藏", "分享", "关注", "粉丝",
        "发布", "编辑", "删除", "举报", "回复",
        "登录", "注册", "设置", "首页", "发现",
        "消息", "我的", "搜索", "热门", "推荐"
    ],

    # 评论区域识别模式
    "comment_section_patterns": [
        r"共有\d+条评论",
        r"共\d+条评论",
        r"\d+条评论",
        r"评论\s*\(\d+\)",
        r"评论\s*\d+",
        r"Comments\s*\(\d+\)",
        r"\d+\s*Comments"
    ],

    # 评论区域缓冲距离（像素）
    "comment_section_buffer": 50,
}

# 权限处理配置
PERMISSION_CONFIG = {
    # 权限按钮关键词
    "permission_keywords": ["允许", "Allow", "确定", "OK", "同意", "Accept"],

    # 权限处理超时时间（秒）
    "permission_timeout": 10,
}
