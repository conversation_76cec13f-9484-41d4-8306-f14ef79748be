#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书自动化脚本
使用 Airtest 完成手机打开浏览器搜索链接，点击允许，跳转至小红书帖子，滑动帖子获取评论
"""

import time
import re
from airtest.core.api import *
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from config import *

class XHSAutomation:
    def __init__(self, device_uri=None):
        """
        初始化自动化类
        :param device_uri: 设备连接URI，如果为None则自动连接第一个设备
        """
        self.device_uri = device_uri or DEVICE_CONFIG["device_uri"]
        self.poco = None
        self.target_url = URL_CONFIG["target_url"]
        self.browser_package = BROWSER_CONFIG["package_name"]
        self.comments = []

    def connect_device(self):
        """连接设备"""
        try:
            if self.device_uri:
                connect_device(self.device_uri)
            else:
                # 手动连接第一个可用设备
                from airtest.core.android.adb import ADB
                adb = ADB()
                devices = adb.devices()

                if not devices:
                    print("❌ 未发现任何设备")
                    return False

                # 连接第一个设备
                device_id = devices[0][0]
                connect_device(f"Android:///{device_id}")
                print(f"📱 已连接设备: {device_id}")

            # 初始化 Poco
            self.poco = AndroidUiautomationPoco(use_airtest_input=True, screenshot_each_action=False)
            print("✅ 设备连接成功")
            return True
        except Exception as e:
            print(f"❌ 设备连接失败: {e}")
            return False

    def open_browser(self):
        """打开浏览器应用"""
        try:
            print("🚀 正在打开浏览器...")
            start_app(self.browser_package)
            time.sleep(BROWSER_CONFIG["startup_wait"])
            print("✅ 浏览器已打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return False

    def navigate_to_url(self):
        """导航到目标URL"""
        try:
            print("🔍 正在导航到小红书链接...")

            # 第一步：尝试找到并点击地址栏
            if not self._click_address_bar():
                return False

            # 第二步：输入URL
            if not self._input_url():
                return False

            # 第三步：点击搜索/前往按钮
            if not self._click_search_button():
                # 如果找不到搜索按钮，尝试按回车键
                print("⚠️ 未找到搜索按钮，尝试按回车键...")
                keyevent("KEYCODE_ENTER")

            time.sleep(URL_CONFIG["page_load_wait"])  # 等待页面加载
            print("✅ 已导航到目标链接")
            return True

        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False

    def _click_address_bar(self):
        """点击地址栏 - 增强版"""
        print("🎯 正在查找地址栏...")

        # 第一轮：通过ResourceId查找地址栏
        print("📍 第一轮：通过ResourceId查找...")
        address_bar_selectors = [
            "com.android.browser:id/url",
            "android:id/url",
            "url",
            "address_bar",
            "omnibox",
            "com.android.browser:id/location_bar",
            "com.android.browser:id/url_bar",
            "com.android.browser:id/addressbar",
            "com.android.browser:id/search_box",
            "com.android.browser:id/search_or_url"
        ]

        for selector in address_bar_selectors:
            try:
                if self.poco(resourceId=selector).exists():
                    print(f"✅ 找到地址栏(ResourceId): {selector}")
                    self.poco(resourceId=selector).click()
                    time.sleep(2)
                    return True
                else:
                    print(f"❌ 未找到: {selector}")
            except Exception as e:
                print(f"❌ 测试 {selector} 失败: {e}")
                continue

        # 第二轮：通过文本查找地址栏
        print("📍 第二轮：通过文本查找...")
        address_bar_texts = [
            "搜索或输入网址", "输入网址", "地址栏", "搜索栏",
            "Search or type URL", "Search", "Address bar",
            "搜索", "网址", "URL", "输入搜索内容"
        ]

        for text in address_bar_texts:
            try:
                if self.poco(text=text).exists():
                    print(f"✅ 通过文本找到地址栏: {text}")
                    self.poco(text=text).click()
                    time.sleep(2)
                    return True
                else:
                    print(f"❌ 未找到文本: {text}")
            except Exception as e:
                print(f"❌ 测试文本 {text} 失败: {e}")
                continue

        # 第三轮：通过部分文本匹配查找
        print("📍 第三轮：通过部分文本匹配...")
        partial_texts = ["搜索", "输入", "网址", "Search", "URL"]

        for partial_text in partial_texts:
            try:
                elements = self.poco(textMatches=f".*{partial_text}.*")
                for element in elements:
                    try:
                        element_text = element.get_text()
                        print(f"尝试点击包含'{partial_text}'的元素: {element_text}")
                        element.click()
                        time.sleep(2)
                        return True
                    except:
                        continue
            except:
                continue

        # 第四轮：通过EditText类型查找（地址栏通常是EditText）
        print("📍 第四轮：查找EditText类型元素...")
        try:
            edit_texts = self.poco("android.widget.EditText")
            for i, edit_text in enumerate(edit_texts):
                try:
                    # 获取EditText的位置，通常地址栏在屏幕上方
                    pos = edit_text.get_position()
                    screen_height = device().display_info['height']

                    # 如果EditText在屏幕上半部分，可能是地址栏
                    if pos[1] < screen_height * 0.3:
                        print(f"✅ 找到可能的地址栏EditText {i+1}")
                        edit_text.click()
                        time.sleep(2)
                        return True
                except:
                    continue
        except:
            pass

        # 第五轮：智能位置点击
        print("📍 第五轮：智能位置点击...")
        screen_width = device().display_info['width']
        screen_height = device().display_info['height']

        # 尝试多个可能的地址栏位置
        positions = [
            (screen_width // 2, screen_height // 10),      # 屏幕上方中央
            (screen_width // 2, screen_height // 8),       # 稍微靠下一点
            (screen_width // 2, screen_height // 6),       # 再靠下一点
            (screen_width * 0.3, screen_height // 10),     # 左侧
            (screen_width * 0.7, screen_height // 10),     # 右侧
        ]

        for i, pos in enumerate(positions):
            try:
                print(f"尝试点击位置 {i+1}: ({pos[0]}, {pos[1]})")
                touch(pos)
                time.sleep(2)

                # 检查是否成功激活了输入框（通常会弹出键盘）
                if self._check_keyboard_visible():
                    print("✅ 成功激活输入框（检测到键盘）")
                    return True

            except Exception as e:
                print(f"❌ 位置点击失败: {e}")
                continue

        print("⚠️ 所有地址栏查找方法都失败了")
        return False

    def _check_keyboard_visible(self):
        """检查键盘是否可见"""
        try:
            # 检查常见的键盘元素
            keyboard_indicators = [
                "android.inputmethodservice.KeyboardView",
                "com.android.inputmethod",
                "输入法"
            ]

            for indicator in keyboard_indicators:
                try:
                    if self.poco(className=indicator).exists():
                        return True
                except:
                    continue

            # 检查是否有键盘按键
            keyboard_keys = ["q", "w", "e", "r", "t", "y", "1", "2", "3"]
            for key in keyboard_keys:
                try:
                    if self.poco(text=key).exists():
                        return True
                except:
                    continue

            return False
        except:
            return False

    def _input_url(self):
        """输入URL"""
        print("⌨️ 正在输入URL...")

        try:
            # 清空当前内容
            keyevent("KEYCODE_CTRL_A")  # 全选
            time.sleep(0.5)
            keyevent("KEYCODE_DEL")     # 删除
            time.sleep(0.5)

            # 输入URL
            text(self.target_url)
            time.sleep(1)
            print("✅ URL输入完成")
            return True

        except Exception as e:
            print(f"❌ URL输入失败: {e}")
            return False

    def _click_search_button(self):
        """点击搜索/前往按钮"""
        print("🔍 正在查找搜索按钮...")

        # 第一轮：尝试通过ResourceId查找
        search_button_selectors = [
            "com.android.browser:id/go_button",
            "com.android.browser:id/search_button",
            "com.android.browser:id/url_bar_go_button",
            "go_button",
            "search_button",
            "navigate_button",
            "android:id/button1"
        ]

        for selector in search_button_selectors:
            try:
                if self.poco(resourceId=selector).exists():
                    print(f"✅ 找到搜索按钮(ResourceId): {selector}")
                    self.poco(resourceId=selector).click()
                    time.sleep(1)
                    return True
            except:
                continue

        # 第二轮：尝试通过文本查找搜索按钮
        search_button_texts = ["前往", "搜索", "Go", "Search", "→", "➤", "确定", "OK"]
        for text in search_button_texts:
            try:
                if self.poco(text=text).exists():
                    print(f"✅ 通过文本找到搜索按钮: {text}")
                    self.poco(text=text).click()
                    time.sleep(1)
                    return True
            except:
                continue

        # 第三轮：查找键盘上的搜索/前往键
        keyboard_search_texts = ["前往", "搜索", "完成", "Done", "Enter"]
        for text in keyboard_search_texts:
            try:
                if self.poco(text=text).exists():
                    print(f"✅ 找到键盘上的搜索键: {text}")
                    self.poco(text=text).click()
                    time.sleep(1)
                    return True
            except:
                continue

        # 第四轮：尝试查找按钮类型的元素
        try:
            buttons = self.poco("android.widget.Button")
            for button in buttons:
                try:
                    button_text = button.get_text()
                    if button_text and any(keyword in button_text for keyword in ["前往", "Go", "搜索", "Search"]):
                        print(f"✅ 找到按钮元素: {button_text}")
                        button.click()
                        time.sleep(1)
                        return True
                except:
                    continue
        except:
            pass

        # 第五轮：尝试查找ImageButton类型的元素（通常是图标按钮）
        try:
            image_buttons = self.poco("android.widget.ImageButton")
            for button in image_buttons:
                try:
                    # 检查按钮的位置，通常搜索按钮在地址栏右侧
                    button_pos = button.get_position()
                    screen_width = device().display_info['width']

                    # 如果按钮在屏幕右侧（可能是搜索按钮）
                    if button_pos[0] > screen_width * 0.7:
                        print(f"✅ 找到可能的搜索图标按钮")
                        button.click()
                        time.sleep(1)
                        return True
                except:
                    continue
        except:
            pass

        print("⚠️ 未找到搜索按钮")
        return False

    def handle_permissions(self):
        """处理权限弹窗，点击允许"""
        try:
            print("🔐 正在处理权限弹窗...")

            # 等待并查找权限相关的按钮
            permission_keywords = PERMISSION_CONFIG["permission_keywords"]

            for _ in range(PERMISSION_CONFIG["permission_timeout"]):  # 等待权限弹窗
                for keyword in permission_keywords:
                    try:
                        if self.poco(text=keyword).exists():
                            self.poco(text=keyword).click()
                            print(f"✅ 已点击 '{keyword}' 按钮")
                            time.sleep(2)
                            return True
                    except:
                        pass

                time.sleep(1)

            print("ℹ️ 未发现权限弹窗或已处理完成")
            return True

        except Exception as e:
            print(f"❌ 处理权限失败: {e}")
            return False

    def wait_for_xhs_page(self):
        """等待小红书页面加载完成"""
        try:
            print("⏳ 等待小红书页面加载...")

            # 等待页面加载，查找小红书特征元素
            for _ in range(URL_CONFIG["page_load_timeout"]):  # 等待页面加载
                # 检查是否有小红书相关的元素
                if (self.poco(textMatches=".*小红书.*").exists() or
                    self.poco(textMatches=".*评论.*").exists() or
                    self.poco(textMatches=".*点赞.*").exists()):
                    print("✅ 小红书页面已加载")
                    time.sleep(3)  # 额外等待确保完全加载
                    return True

                time.sleep(1)

            print("⚠️ 页面加载超时，继续执行...")
            return True

        except Exception as e:
            print(f"❌ 等待页面加载失败: {e}")
            return False

    def scroll_and_collect_comments(self, max_scrolls=None):
        """滑动页面并收集评论"""
        try:
            print("📜 开始滑动页面收集评论...")

            # 使用配置文件中的默认值
            if max_scrolls is None:
                max_scrolls = SCROLL_CONFIG["max_scrolls"]

            # 清空之前的评论
            self.comments = []

            for scroll_count in range(max_scrolls):
                print(f"🔄 第 {scroll_count + 1} 次滑动...")

                # 获取当前页面的评论
                current_comments = self.extract_comments_from_current_view()

                # 添加新评论到列表
                for comment in current_comments:
                    if comment not in self.comments:
                        self.comments.append(comment)

                # 向下滑动
                swipe((device().display_info['width'] // 2, device().display_info['height'] * 0.7),
                      (device().display_info['width'] // 2, device().display_info['height'] * 0.3),
                      duration=SCROLL_CONFIG["scroll_duration"])

                time.sleep(SCROLL_CONFIG["scroll_wait"])  # 等待页面稳定

            print(f"✅ 滑动完成，共收集到 {len(self.comments)} 条评论")
            return True

        except Exception as e:
            print(f"❌ 滑动收集评论失败: {e}")
            return False

    def extract_comments_from_current_view(self):
        """从当前视图中提取评论"""
        comments = []
        try:
            # 获取所有文本元素
            all_text_elements = self.poco(textMatches=".*")

            for element in all_text_elements:
                try:
                    text_content = element.get_text()
                    if text_content and self.is_likely_comment(text_content):
                        comments.append(text_content.strip())
                except:
                    continue

            return comments

        except Exception as e:
            print(f"⚠️ 提取评论时出错: {e}")
            return comments

    def is_likely_comment(self, text):
        """判断文本是否可能是评论"""
        if not text or len(text.strip()) < 2:
            return False

        # 过滤掉一些明显不是评论的文本
        exclude_keywords = COMMENT_FILTER["exclude_keywords"]

        # 如果包含排除关键词，可能不是评论
        for keyword in exclude_keywords:
            if keyword in text:
                return False

        # 如果文本长度合理且包含中文字符，可能是评论
        min_len = COMMENT_FILTER["min_length"]
        max_len = COMMENT_FILTER["max_length"]
        if min_len <= len(text) <= max_len and re.search(r'[\u4e00-\u9fff]', text):
            return True

        return False

    def print_comments(self):
        """输出收集到的评论到控制台"""
        print("\n" + "="*50)
        print("📝 收集到的评论内容:")
        print("="*50)

        if not self.comments:
            print("❌ 未收集到任何评论")
            return

        for i, comment in enumerate(self.comments, 1):
            print(f"\n💬 评论 {i}:")
            print(f"   {comment}")

        print(f"\n📊 总计: {len(self.comments)} 条评论")
        print("="*50)

    def run_automation(self):
        """运行完整的自动化流程"""
        print("🤖 开始执行小红书自动化任务...")

        # 1. 连接设备
        if not self.connect_device():
            return False

        # 2. 打开浏览器
        if not self.open_browser():
            return False

        # 3. 导航到目标URL
        if not self.navigate_to_url():
            return False

        # 4. 处理权限弹窗
        if not self.handle_permissions():
            return False

        # 5. 等待小红书页面加载
        if not self.wait_for_xhs_page():
            return False

        # 6. 滑动并收集评论
        if not self.scroll_and_collect_comments():
            return False

        # 7. 输出评论到控制台
        self.print_comments()

        print("✅ 自动化任务执行完成!")
        return True


def main():
    """主函数"""
    print("🎯 小红书评论收集自动化脚本")
    print("目标链接: https://www.xiaohongshu.com/discovery/item/640745c5000000001303ed88")
    print("浏览器包名: com.android.browser")
    print("-" * 60)

    # 创建自动化实例
    automation = XHSAutomation()

    # 运行自动化任务
    success = automation.run_automation()

    if success:
        print("\n🎉 任务执行成功!")
    else:
        print("\n❌ 任务执行失败!")


if __name__ == "__main__":
    main()
